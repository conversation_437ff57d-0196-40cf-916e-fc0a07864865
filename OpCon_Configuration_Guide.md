# OpCon Configuration Guide for Facebook Ad Scraper

## Overview
This guide provides instructions for configuring OpCon to work with the enhanced Facebook Ad Library scraper that now provides distinct exit codes for different outcomes.

## Exit Code Mapping

### Success Codes
- **0**: Success with results found - Ad data was successfully extracted and links were generated
- **10**: Success with no results found - <PERSON>ript completed successfully but no ads matched the criteria (expected outcome, not an error)

### Error Codes
- **11**: Python not found in PATH (batch script error)
- **12**: Failed to install requests package (batch script error)
- **13**: Failed to install beautifulsoup4 package (batch script error)
- **21**: Webpage fetch error (short HTML or network issues)
- **22**: Issues finding/parsing ad data JSON structure
- **23**: Invalid --start_date format
- **24**: CSV output file writing error
- **99**: Critical error - could not change to script directory (batch script error)

## OpCon Job Configuration

### Command Line
```
run_scraper.bat --query "Your Search Query" --country US --output "C:\path\to\output.csv" --debug
```

### Start Directory
Set to the directory containing both `run_scraper.bat` and `fb_ad_scraper.py`:
```
C:\git\python\facebook-ad-scraper
```

### Arguments Examples
- Basic usage: `--query "Educators Credit Union"`
- With date filter: `--query "Bank Name" --start_date "2024-01-01"`
- With output file: `--query "Credit Union" --output "C:\OpCon\output\ads.csv"`
- With debug: `--query "Financial Institution" --debug`

## OpCon Success/Failure Criteria Configuration

### Option 1: Treat "No Results" as Success
If you want both "results found" and "no results" to be considered successful:
- **Success Exit Codes**: 0, 10
- **Failure Exit Codes**: All others (11, 12, 13, 21, 22, 23, 24, 99)

### Option 2: Different Actions Based on Results
If you want different downstream actions based on whether results were found:

#### Job for "Results Found" (Exit Code 0)
- Configure job to succeed only on exit code 0
- Set up downstream jobs that process the found results

#### Job for "No Results Found" (Exit Code 10)
- Configure job to succeed only on exit code 10
- Set up different downstream actions (notifications, alternative searches, etc.)

#### Job for "Any Success" (Exit Codes 0 or 10)
- Configure job to succeed on exit codes 0 and 10
- Use job dependencies or conditions to handle different outcomes

## Recommended OpCon Job Setup

### Primary Job: "FB_Ad_Scraper"
- **Command**: `run_scraper.bat --query "Educators Credit Union" --output "C:\OpCon\FB_Ads\daily_ads.csv" --debug`
- **Start Directory**: `C:\git\python\facebook-ad-scraper`
- **Success Exit Codes**: 0, 10
- **Failure Exit Codes**: 11, 12, 13, 21, 22, 23, 24, 99

### Conditional Downstream Jobs

#### Job: "Process_Found_Ads" (Runs when results found)
- **Dependency**: FB_Ad_Scraper with exit code 0
- **Action**: Process the CSV file with found ad links

#### Job: "Handle_No_Results" (Runs when no results)
- **Dependency**: FB_Ad_Scraper with exit code 10
- **Action**: Send notification or trigger alternative search

#### Job: "Handle_Errors" (Runs on failures)
- **Dependency**: FB_Ad_Scraper with exit codes 11, 12, 13, 21, 22, 23, 24, 99
- **Action**: Send error notifications, retry logic, or escalation

## Monitoring and Alerting

### Success Monitoring
- Monitor for regular successful completion (exit codes 0 or 10)
- Track the ratio of "results found" vs "no results" over time

### Error Monitoring
- **Exit Code 21**: Network/Facebook blocking issues - may need IP rotation or delay adjustments
- **Exit Code 22**: Facebook page structure changes - may need script updates
- **Exit Code 23**: Date format issues - check job parameters
- **Exit Code 24**: File system issues - check permissions and disk space
- **Exit Codes 11-13**: Environment issues - check Python installation and packages

## Troubleshooting

### Common Issues
1. **Exit Code 21**: Facebook may be blocking requests
   - Solution: Add delays between runs, check network connectivity
   
2. **Exit Code 22**: Facebook changed their page structure
   - Solution: Update the scraper script to handle new JSON structure
   
3. **Exit Code 10 frequently**: Query may be too specific or no current ads
   - Solution: Broaden search terms or adjust date filters

### Debug Mode
Always use `--debug` flag in production to get detailed logging in stderr for troubleshooting.
