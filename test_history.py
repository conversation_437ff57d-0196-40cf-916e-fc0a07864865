#!/usr/bin/env python3
"""
Test script to verify the ad history functionality works correctly.
"""

import os
import sys

# Add the current directory to the path so we can import from fb_ad_scraper
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fb_ad_scraper import load_ad_history, save_ad_to_history

def test_history_functions():
    """Test the ad history functions."""
    test_file = "test_ad_history.txt"
    
    # Clean up any existing test file
    if os.path.exists(test_file):
        os.remove(test_file)
    
    print("Testing ad history functions...")
    
    # Test 1: Load empty history
    print("Test 1: Loading empty history")
    history = load_ad_history(test_file, debug=True)
    assert len(history) == 0, f"Expected empty history, got {len(history)} items"
    print("✓ Empty history loaded correctly")
    
    # Test 2: Save ad to history
    print("\nTest 2: Saving ad to history")
    test_ad_id = "123456789"
    save_ad_to_history(test_ad_id, test_file, debug=True)
    
    # Test 3: Load history with one item
    print("\nTest 3: Loading history with one item")
    history = load_ad_history(test_file, debug=True)
    assert len(history) == 1, f"Expected 1 item in history, got {len(history)}"
    assert test_ad_id in history, f"Expected {test_ad_id} in history"
    print("✓ History with one item loaded correctly")
    
    # Test 4: Save another ad to history
    print("\nTest 4: Saving another ad to history")
    test_ad_id2 = "987654321"
    save_ad_to_history(test_ad_id2, test_file, debug=True)
    
    # Test 5: Load history with two items
    print("\nTest 5: Loading history with two items")
    history = load_ad_history(test_file, debug=True)
    assert len(history) == 2, f"Expected 2 items in history, got {len(history)}"
    assert test_ad_id in history, f"Expected {test_ad_id} in history"
    assert test_ad_id2 in history, f"Expected {test_ad_id2} in history"
    print("✓ History with two items loaded correctly")
    
    # Test 6: Check file contents
    print("\nTest 6: Checking file contents")
    with open(test_file, 'r') as f:
        lines = f.readlines()
    assert len(lines) == 2, f"Expected 2 lines in file, got {len(lines)}"
    assert lines[0].strip() == test_ad_id, f"Expected first line to be {test_ad_id}"
    assert lines[1].strip() == test_ad_id2, f"Expected second line to be {test_ad_id2}"
    print("✓ File contents are correct")
    
    # Clean up
    os.remove(test_file)
    print("\n✓ All tests passed!")

if __name__ == "__main__":
    test_history_functions()
